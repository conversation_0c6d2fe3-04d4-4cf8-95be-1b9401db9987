<!DOCTYPE html>
<html lang="ka">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript & TypeScript ქვიზი</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Georgian:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans Georgian', sans-serif;
        }
        .quiz-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .card {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .option-btn {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .option-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .option-btn.correct {
            background: linear-gradient(135deg, #10b981, #059669);
            border-color: #059669;
            color: white;
        }
        .option-btn.incorrect {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            border-color: #dc2626;
            color: white;
        }
        .progress-bar {
            background: linear-gradient(90deg, #10b981, #059669);
            height: 8px;
            border-radius: 4px;
            transition: width 0.5s ease;
        }
        .category-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        .explanation {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border-left: 4px solid #0ea5e9;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="quiz-container">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
                <i class="fas fa-code mr-3"></i>JavaScript & TypeScript ქვიზი
            </h1>
            <p class="text-xl text-white opacity-90">შეამოწმეთ თქვენი ცოდნა პროგრამირებაში</p>
        </div>

        <!-- Main Menu -->
        <div id="mainMenu" class="max-w-4xl mx-auto">
            <div class="grid md:grid-cols-2 gap-6">
                <!-- JavaScript Beginner -->
                <div class="category-card card rounded-2xl p-6 shadow-xl" onclick="startQuiz('jsBeginner')">
                    <div class="text-center">
                        <div class="text-4xl mb-4">
                            <i class="fab fa-js-square text-yellow-500"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-3">JavaScript დამწყები</h3>
                        <p class="text-gray-600 mb-4">ძირითადი კონცეფციები და სინტაქსი</p>
                        <div class="flex justify-center space-x-4 text-sm text-gray-500">
                            <span><i class="fas fa-clock mr-1"></i>10 წუთი</span>
                            <span><i class="fas fa-question-circle mr-1"></i>8 კითხვა</span>
                        </div>
                    </div>
                </div>

                <!-- JavaScript Advanced -->
                <div class="category-card card rounded-2xl p-6 shadow-xl" onclick="startQuiz('jsAdvanced')">
                    <div class="text-center">
                        <div class="text-4xl mb-4">
                            <i class="fab fa-js-square text-yellow-600"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-3">JavaScript მოწინავე</h3>
                        <p class="text-gray-600 mb-4">რთული კონცეფციები და შაბლონები</p>
                        <div class="flex justify-center space-x-4 text-sm text-gray-500">
                            <span><i class="fas fa-clock mr-1"></i>15 წუთი</span>
                            <span><i class="fas fa-question-circle mr-1"></i>8 კითხვა</span>
                        </div>
                    </div>
                </div>

                <!-- TypeScript Beginner -->
                <div class="category-card card rounded-2xl p-6 shadow-xl" onclick="startQuiz('tsBeginner')">
                    <div class="text-center">
                        <div class="text-4xl mb-4">
                            <i class="fas fa-code text-blue-500"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-3">TypeScript დამწყები</h3>
                        <p class="text-gray-600 mb-4">ტიპები და ძირითადი გამოყენება</p>
                        <div class="flex justify-center space-x-4 text-sm text-gray-500">
                            <span><i class="fas fa-clock mr-1"></i>10 წუთი</span>
                            <span><i class="fas fa-question-circle mr-1"></i>8 კითხვა</span>
                        </div>
                    </div>
                </div>

                <!-- TypeScript Advanced -->
                <div class="category-card card rounded-2xl p-6 shadow-xl" onclick="startQuiz('tsAdvanced')">
                    <div class="text-center">
                        <div class="text-4xl mb-4">
                            <i class="fas fa-code text-blue-600"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-3">TypeScript მოწინავე</h3>
                        <p class="text-gray-600 mb-4">მოწინავე ტიპები და არქიტექტურა</p>
                        <div class="flex justify-center space-x-4 text-sm text-gray-500">
                            <span><i class="fas fa-clock mr-1"></i>15 წუთი</span>
                            <span><i class="fas fa-question-circle mr-1"></i>8 კითხვა</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats Section -->
            <div class="mt-8 card rounded-2xl p-6 shadow-xl">
                <h3 class="text-xl font-bold text-gray-800 mb-4 text-center">
                    <i class="fas fa-chart-line mr-2"></i>თქვენი სტატისტიკა
                </h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600" id="totalQuizzes">0</div>
                        <div class="text-sm text-gray-600">მოსრულებული ქვიზები</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600" id="averageScore">0%</div>
                        <div class="text-sm text-gray-600">საშუალო ქულა</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600" id="bestCategory">-</div>
                        <div class="text-sm text-gray-600">საუკეთესო კატეგორია</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600" id="timeSpent">0 წთ</div>
                        <div class="text-sm text-gray-600">დახარჯული დრო</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quiz Interface -->
        <div id="quizInterface" class="max-w-4xl mx-auto hidden">
            <!-- Quiz Header -->
            <div class="card rounded-2xl p-6 shadow-xl mb-6">
                <div class="flex justify-between items-center mb-4">
                    <button onclick="backToMenu()" class="text-blue-600 hover:text-blue-800 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>მთავარ მენიუში დაბრუნება
                    </button>
                    <div class="text-right">
                        <div class="text-sm text-gray-600">კითხვა <span id="currentQuestion">1</span> / <span id="totalQuestions">8</span></div>
                        <div class="text-sm text-gray-600">ქულა: <span id="currentScore">0</span></div>
                    </div>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="progress-bar h-2 rounded-full" id="progressBar" style="width: 0%"></div>
                </div>
            </div>

            <!-- Question Card -->
            <div class="card rounded-2xl p-8 shadow-xl mb-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-6" id="questionTitle">კითხვა იტვირთება...</h2>
                <pre class="bg-gray-100 p-4 rounded-lg mb-6 overflow-x-auto" id="codeBlock" style="display:none;"><code></code></pre>
                <div id="optionsContainer" class="space-y-3">
                    <!-- Options will be populated here -->
                </div>
            </div>

            <!-- Explanation Card -->
            <div id="explanationCard" class="card rounded-2xl p-6 shadow-xl mb-6 hidden">
                <div class="explanation rounded-lg p-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-3">
                        <i class="fas fa-lightbulb mr-2"></i>ახსნა
                    </h3>
                    <div id="explanationText" class="text-gray-700"></div>
                </div>
                <button onclick="nextQuestion()" class="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                    <i class="fas fa-arrow-right mr-2"></i>შემდეგი კითხვა
                </button>
            </div>
        </div>

        <!-- Results Screen -->
        <div id="resultsScreen" class="max-w-4xl mx-auto hidden">
            <div class="card rounded-2xl p-8 shadow-xl text-center">
                <div class="text-6xl mb-6" id="resultIcon">🎉</div>
                <h2 class="text-3xl font-bold text-gray-800 mb-4" id="resultTitle">შესანიშნავია!</h2>
                <div class="text-6xl font-bold text-blue-600 mb-6" id="finalScore">0%</div>
               
                <div class="grid md:grid-cols-3 gap-6 mb-8">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600" id="correctAnswers">0</div>
                        <div class="text-gray-600">სწორი პასუხი</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600" id="wrongAnswers">0</div>
                        <div class="text-gray-600">არასწორი პასუხი</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600" id="timeTaken">0 წთ</div>
                        <div class="text-gray-600">დახარჯული დრო</div>
                    </div>
                </div>

                <div class="space-y-4">
                    <button onclick="restartQuiz()" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg mr-4 transition-colors">
                        <i class="fas fa-redo mr-2"></i>კვლავ სცადეთ
                    </button>
                    <button onclick="backToMenu()" class="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-lg transition-colors">
                        <i class="fas fa-home mr-2"></i>მთავარ მენიუში დაბრუნება
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Quiz Data
        const quizData = {
            jsBeginner: [
                {
                    question: "რა არის JavaScript-ში var-ისა და let-ის ძირითადი განსხვავება?",
                    code: `var x = 1;
let y = 2;
{
    var x = 3;
    let y = 4;
    console.log(x, y); // ?
}
console.log(x, y); // ?`,
                    options: [
                        "var ქმნის ფუნქციური scoping-ს, let კი block scoping-ს",
                        "let ქმნის ფუნქციური scoping-ს, var კი block scoping-ს",
                        "ორივე იდენტურია",
                        "var უფრო სწრაფია let-ზე"
                    ],
                    correct: 0,
                    explanation: "var ქმნის ფუნქციურ ან გლობალურ scope-ს, ხოლო let ქმნის block scope-ს. ამ მაგალითში var x გადაიწერება block-ის შიგნით, ხოლო let y დარჩება განცალკევებული block-ის შიგნით."
                },
                {
                    question: "როგორ შეიძლება array-ს ელემენტის დამატება ბოლოში?",
                    code: `let fruits = ['apple', 'banana'];
// ელემენტის დამატება`,
                    options: [
                        "fruits.push('orange')",
                        "fruits.add('orange')",
                        "fruits.append('orange')",
                        "fruits.insert('orange')"
                    ],
                    correct: 0,
                    explanation: "push() მეთოდი ამატებს ერთ ან მეტ ელემენტს array-ს ბოლოში და აბრუნებს array-ს ახალ სიგრძეს."
                },
                {
                    question: "რა იქნება ამ კოდის შედეგი?",
                    code: `function greet(name = 'სტუმარო') {
    return 'გამარჯობა, ' + name + '!';
}
console.log(greet());`,
                    options: [
                        "გამარჯობა, სტუმარო!",
                        "გამარჯობა, undefined!",
                        "Error",
                        "გამარჯობა, !"
                    ],
                    correct: 0,
                    explanation: "JavaScript-ში ფუნქციის პარამეტრებს შეუძლიათ default values-ის ქონა. თუ არგუმენტი არ არის გადმოცემული, გამოიყენება default value."
                },
                {
                    question: "როგორ მოვიღოთ object-ის property?",
                    code: `let person = {
    name: 'გიორგი',
    age: 25,
    city: 'თბილისი'
};`,
                    options: [
                        "person.name ან person['name']",
                        "მხოლოდ person.name",
                        "მხოლოდ person['name']",
                        "person->name"
                    ],
                    correct: 0,
                    explanation: "JavaScript-ში object-ის property-ს მიღება შეიძლება ორი გზით: dot notation (person.name) ან bracket notation (person['name'])."
                },
                {
                    question: "რა განსხვავებაა == და === ოპერატორებს შორის?",
                    code: `console.log(5 == '5');  // ?
console.log(5 === '5'); // ?`,
                    options: [
                        "== ამოწმებს მნიშვნელობას, === კი მნიშვნელობასა და ტიპს",
                        "== ამოწმებს ტიპს, === კი მნიშვნელობას",
                        "ორივე იდენტურია",
                        "=== უფრო ნელია"
                    ],
                    correct: 0,
                    explanation: "== (loose equality) ამოწმებს მხოლოდ მნიშვნელობას type coercion-ის შემდეგ, ხოლო === (strict equality) ამოწმებს როგორც მნიშვნელობას, ასევე ტიპს."
                },
                {
                    question: "როგორ მუშაობს for...of loop?",
                    code: `let numbers = [1, 2, 3, 4, 5];
for (let num of numbers) {
    console.log(num);
}`,
                    options: [
                        "იტერირებს array-ს მნიშვნელობებზე",
                        "იტერირებს array-ს ინდექსებზე",
                        "იტერირებს object-ის properties-ზე",
                        "არ მუშაობს arrays-თან"
                    ],
                    correct: 0,
                    explanation: "for...of loop იტერირებს iterable objects-ის (arrays, strings, etc.) მნიშვნელობებზე, არა ინდექსებზე."
                },
                {
                    question: "რა არის arrow function-ის სინტაქსი?",
                    code: `// რეგულარული ფუნქცია
function add(a, b) {
    return a + b;
}
// arrow function
const add = ?`,
                    options: [
                        "(a, b) => a + b",
                        "(a, b) -> a + b",
                        "function(a, b) => a + b",
                        "(a, b) return a + b"
                    ],
                    correct: 0,
                    explanation: "Arrow functions ხასიათდება ამ სინტაქსით: (parameters) => expression ან (parameters) => { statements }. ისინი უფრო კომპაქტურია და აქვთ განსხვავებული this binding."
                },
                {
                    question: "როგორ მოვძებნოთ DOM ელემენტი ID-ით?",
                    code: `<!-- HTML -->
<div id="myDiv">ტექსტი</div>

// JavaScript
let element = ?`,
                    options: [
                        "document.getElementById('myDiv')",
                        "document.getElement('myDiv')",
                        "document.findById('myDiv')",
                        "document.selectId('myDiv')"
                    ],
                    correct: 0,
                    explanation: "document.getElementById() მეთოდი აბრუნებს ელემენტს მოცემული ID-ით. ეს არის ყველაზე სწრაფი გზა ელემენტის მოსაძებნად ID-ით."
                }
            ],

            jsAdvanced: [
                {
                    question: "რა არის closure და როგორ მუშაობს?",
                    code: `function outerFunction(x) {
    return function innerFunction(y) {
        return x + y;
    };
}
const addFive = outerFunction(5);
console.log(addFive(3)); // ?`,
                    options: [
                        "8 - closure ინახავს outer scope-ის ცვლადებს",
                        "Error - x არ არის განსაზღვრული",
                        "undefined",
                        "3"
                    ],
                    correct: 0,
                    explanation: "Closure წარმოადგენს ფუნქციას მის lexical environment-თან ერთად. Inner function-ს აქვს წვდომა outer function-ის ცვლადებთან დაბრუნების შემდეგაც."
                },
                {
                    question: "როგორ მუშაობს Promise.all()?",
                    code: `const promise1 = Promise.resolve(3);
const promise2 = new Promise(resolve =>
    setTimeout(() => resolve('foo'), 1000));
const promise3 = Promise.resolve(42);

Promise.all([promise1, promise2, promise3])
    .then(values => console.log(values));`,
                    options: [
                        "[3, 'foo', 42] ყველა Promise-ის დასრულების შემდეგ",
                        "მხოლოდ პირველი Promise-ის შედეგი",
                        "Error თუ რომელიმე Promise ვერ სრულდება",
                        "undefined"
                    ],
                    correct: 0,
                    explanation: "Promise.all() ლოდის ყველა Promise-ის დასრულებას და აბრუნებს array-ს ყველა შედეგით იმავე თანმიმდევრობით."
                },
                {
                    question: "რა არის prototypal inheritance JavaScript-ში?",
                    code: `function Animal(name) {
    this.name = name;
}
Animal.prototype.speak = function() {
    return this.name + ' makes a sound';
};

function Dog(name) {
    Animal.call(this, name);
}
Dog.prototype = Object.create(Animal.prototype);
Dog.prototype.constructor = Dog;`,
                    options: [
                        "Object-ები ინარისებენ properties-ს prototype chain-ის მეშვეობით",
                        "მხოლოდ class-based inheritance არსებობს",
                        "Prototype არ გამოიყენება inheritance-ისთვის",
                        "მხოლოდ ES6 classes შეიძლება გამოყენება"
                    ],
                    correct: 0,
                    explanation: "JavaScript იყენებს prototypal inheritance-ს, სადაც ობიექტები ინარისებენ მეთოდებსა და properties-ს მათი prototype chain-იდან."
                },
                {
                    question: "რა განსხვავებაა async/await და Promise-ებს შორის?",
                    code: `// Promise
fetchData()
    .then(data => console.log(data))
    .catch(err => console.error(err));

// async/await
async function getData() {
    try {
        const data = await fetchData();
        console.log(data);
    } catch(err) {
        console.error(err);
    }
}`,
                    options: [
                        "async/await არის Promise-ების syntactic sugar",
                        "async/await სრულიად განსხვავებული ტექნოლოგიაა",
                        "Promise-ები უფრო სწრაფია",
                        "async/await არ იყენებს Promise-ებს"
                    ],
                    correct: 0,
                    explanation: "async/await არის syntactic sugar Promise-ების თავზე, რაც ასინქრონულ კოდს უფრო readable და maintainable ხდის."
                },
                {
                    question: "რა არის event loop JavaScript-ში?",
                    code: `console.log('1');
setTimeout(() => console.log('2'), 0);
console.log('3');
// გამოსავალი: ?`,
                    options: [
                        "1, 3, 2 - Event loop ასინქრონული კოდი callback queue-ში აყენებს",
                        "1, 2, 3",
                        "2, 1, 3",
                        "3, 2, 1"
                    ],
                    correct: 0,
                    explanation: "Event loop არის JavaScript-ის concurrency model-ის ნაწილი. setTimeout callback აყენებს task queue-ში, რომელიც სრულდება call stack-ის დაცლის შემდეგ."
                },
                {
                    question: "რა არის destructuring assignment?",
                    code: `const person = {
    name: 'ანა',
    age: 30,
    city: 'თბილისი'
};
const { name, age } = person;
console.log(name, age); // ?`,
                    options: [
                        "ანა 30 - destructuring ამოიღებს properties-ს",
                        "Error",
                        "undefined undefined",
                        "person.name person.age"
                    ],
                    correct: 0,
                    explanation: "Destructuring assignment საშუალებას იძლევა arrays-იდან ან objects-იდან მნიშვნელობების ამოღება და ცალკე ცვლადებში მინიჭება."
                },
                {
                    question: "როგორ მუშაობს spread operator?",
                    code: `const arr1 = [1, 2, 3];
const arr2 = [4, 5, 6];
const combined = [...arr1, ...arr2];
console.log(combined); // ?`,
                    options: [
                        "[1, 2, 3, 4, 5, 6] - spread აშლის arrays-ს",
                        "[[1, 2, 3], [4, 5, 6]]",
                        "Error",
                        "[1, 2, 3] [4, 5, 6]"
                    ],
                    correct: 0,
                    explanation: "Spread operator (...) აშლის iterable-ს ინდივიდუალურ ელემენტებად, რაც საშუალებას იძლევა arrays-ის ან objects-ის შერწყმისა და კოპირებისთვის."
                },
                {
                    question: "რა არის module system ES6-ში?",
                    code: `// math.js
export const add = (a, b) => a + b;
export default function multiply(a, b) {
    return a * b;
}

// main.js
import multiply, { add } from './math.js';`,
                    options: [
                        "ES6 modules საშუალებას იძლევა კოდის მოდულარიზებისა და reusability-ისთვის",
                        "მხოლოდ CommonJS შეიძლება გამოყენება",
                        "Modules არ არის მხარდაჭერილი",
                        "import/export სინტაქსი არ მუშაობს"
                    ],
                    correct: 0,
                    explanation: "ES6 modules წარმოადგენს სტანდარტულ გზას JavaScript-ში კოდის მოდულარიზებისთვის, რაც საშუალებას იძლევა functionality-ის export/import-ისთვის."
                }
            ],

            tsBeginner: [
                {
                    question: "რა არის TypeScript-ის ძირითადი მიზანი?",
                    code: `// JavaScript
let message = 'Hello';
message = 42; // არ აცდენის შეცდომას

// TypeScript  
let message: string = 'Hello';
message = 42; // შეცდომა`,
                    options: [
                        "JavaScript-ში static typing-ის დამატება",
                        "JavaScript-ის შეცვლა",
                        "ახალი პროგრამირების ენის შექმნა",
                        "მხოლოდ transpilation"
                    ],
                    correct: 0,
                    explanation: "TypeScript ამატებს static type checking-ს JavaScript-ში, რაც ეხმარება დეველოპერებს შეცდომების ადრეულ დაფიქსირებაში development time-ზე."
                },
                {
                    question: "როგორ განვსაზღვროთ ფუნქციის type?",
                    code: `// რა იქნება სწორი სინტაქსი?
function greet(name: string): string {
    return 'Hello, ' + name;
}`,
                    options: [
                        "function greet(name: string): string",
                        "function greet(name string) string",
                        "function greet(name: string) -> string",
                        "function greet(string name): string"
                    ],
                    correct: 0,
                    explanation: "TypeScript-ში ფუნქციის parameter-ის type იწერება parameter-ის სახელის შემდეგ (:), ხოლო return type იწერება ფუნქციის მთელი signature-ის შემდეგ."
                },
                {
                    question: "რა არის interface TypeScript-ში?",
                    code: `interface Person {
    name: string;
    age: number;
    email?: string; // optional
}

const user: Person = {
    name: 'გიორგი',
    age: 25
};`,
                    options: [
                        "Interface განსაზღვრავს object-ის structure-ს",
                        "Interface არის class-ის სახეობა",
                        "Interface იყენება მხოლოდ function-ებისთვის",
                        "Interface არ არის TypeScript-ის ნაწილი"
                    ],
                    correct: 0,
                    explanation: "Interface განსაზღვრავს object-ის shape-ს - რა properties უნდა ჰქონდეს და რა type-ის. ? სიმბოლო კი აღნიშნავს optional property-ს."
                },
                {
                    question: "რა არის Union Types?",
                    code: `let id: string | number;
id = '123';    // ✓
id = 123;      // ✓
id = true;     // ✗ Error`,
                    options: [
                        "Union type საშუალებას იძლევა ცვლადს ჰქონდეს რამდენიმე type-იდან ერთი",
                        "Union type აკავშირებს რამდენიმე interface-ს",
                        "Union type იყენება მხოლოდ arrays-თან",
                        "Union type არ არსებობს TypeScript-ში"
                    ],
                    correct: 0,
                    explanation: "Union type (|) საშუალებას იძლევა ცვლადს ან parameter-ს ჰქონდეს რამდენიმე type-იდან ერთ-ერთი."
                },
                {
                    question: "რა არის Array typing TypeScript-ში?",
                    code: `// რამდენიმე გზა arrays-ის type-ისთვის
let numbers1: number[] = [1, 2, 3];
let numbers2: Array<number> = [1, 2, 3];`,
                    options: [
                        "number[] და Array<number> ორივე სწორია",
                        "მხოლოდ number[] შეიძლება",
                        "მხოლოდ Array<number> შეიძლება",
                        "Arrays-ს type არ შეიძლება განსაზღვრა"
                    ],
                    correct: 0,
                    explanation: "TypeScript-ში array-ის type-ის განსაზღვრის ორი გზაა: element_type[] (მაგ. number[]) ან Array<element_type> (მაგ. Array<number>)."
                },
                {
                    question: "რა არის Generic-ები TypeScript-ში?",
                    code: `function identity<T>(arg: T): T {
    return arg;
}

let output1 = identity<string>('hello');
let output2 = identity<number>(42);`,
                    options: [
                        "Generic-ები საშუალებას იძლევა reusable components-ის შექმნისა type safety-ით",
                        "Generic-ები იყენება მხოლოდ classes-თან",
                        "Generic-ები არ არის TypeScript-ის ნაწილი",
                        "Generic-ები იყენება მხოლოდ arrays-თან"
                    ],
                    correct: 0,
                    explanation: "Generic-ები საშუალებას იძლევა ისეთი components-ის შექმნისა, რომლებიც მუშაობს სხვადასხვა type-ებთან type safety-ის შენარჩუნებით."
                },
                {
                    question: "რა არის Type Assertion?",
                    code: `let someValue: unknown = 'hello world';
let strLength1 = (someValue as string).length;
let strLength2 = (<string>someValue).length;`,
                    options: [
                        "Type assertion საშუალებას იძლევა TypeScript-ს ვუთხრათ ცვლადის type",
                        "Type assertion ცვლის ცვლადის რეალურ type-ს",
                        "Type assertion იყენება მხოლოდ error handling-ისთვის",
                        "Type assertion არ არის რეკომენდებული"
                    ],
                    correct: 0,
                    explanation: "Type assertion საშუალებას იძლევა დეველოპერმა TypeScript-ს უთხრას ცვლადის type compile time-ზე. ორი სინტაქსი არსებობს: as კლაუზული და angle-bracket."
                },
                {
                    question: "რა არის Optional Chaining TypeScript-ში?",
                    code: `interface User {
    name: string;
    address?: {
        street: string;
        city: string;
    };
}

const user: User = { name: 'ანა' };
const city = user.address?.city; // ?`,
                    options: [
                        "Optional chaining (?.) უსაფრთხოდ ამოწმებს nested properties-ს",
                        "Optional chaining იყენება მხოლოდ arrays-თან",
                        "Optional chaining არ არის TypeScript-ის ნაწილი",
                        "Optional chaining აცდენს error-ს"
                    ],
                    correct: 0,
                    explanation: "Optional chaining (?.) საშუალებას იძლევა უსაფრთხოდ მივაკითხოთ nested object properties-ს undefined ან null შეცდომების გარეშე."
                }
            ],

            tsAdvanced: [
                {
                    question: "რა არის Mapped Types?",
                    code: `type Readonly<T> = {
    readonly [P in keyof T]: T[P];
};

interface User {
    name: string;
    age: number;
}

type ReadonlyUser = Readonly<User>;
// { readonly name: string; readonly age: number; }`,
                    options: [
                        "Mapped types ქმნის ახალ types არსებული type-ის transformation-ით",
                        "Mapped types იყენება მხოლოდ arrays-თან",
                        "Mapped types არ შეიძლება custom-ის შექმნა",
                        "Mapped types არ არის TypeScript-ის ნაწილი"
                    ],
                    correct: 0,
                    explanation: "Mapped types საშუალებას იძლევა ახალი type-ების შექმნისა არსებული type-ის transformation-ით, როგორიცაა Readonly, Partial, Pick და სხვა utility types-ები."
                },
                {
                    question: "რა არის Conditional Types?",
                    code: `type ApiResponse<T> = T extends string
    ? { message: T }
    : { data: T };

type StringResponse = ApiResponse<string>;
// { message: string }

type NumberResponse = ApiResponse<number>;
// { data: number }`,
                    options: [
                        "Conditional types საშუალებას იძლევა type-ის განსაზღვრა condition-ის მიხედვით",
                        "Conditional types იყენება მხოლოდ error handling-ისთვის",
                        "Conditional types არ მუშაობს generic-ებთან",
                        "Conditional types arის მხოლოდ built-in utility"
                    ],
                    correct: 0,
                    explanation: "Conditional types საშუალებას იძლევა type-ის განსაზღვრისა condition-ის საფუძველზე, გამოიყენება A extends B ? C : D სინტაქსით."
                },
                {
                    question: "რა არის Declaration Merging?",
                    code: `interface User {
    name: string;
}

interface User {
    age: number;
}

// შედეგი: interface User { name: string; age: number; }`,
                    options: [
                        "Declaration merging აერთიანებს იგივე სახელის რამდენიმე declaration-ს",
                        "Declaration merging იყენება მხოლოდ classes-თან",
                        "Declaration merging აცდენს error-ს",
                        "Declaration merging არ არის რეკომენდებული"
                    ],
                    correct: 0,
                    explanation: "Declaration merging საშუალებას იძლევა TypeScript-მა აერთიანოს იგივე სახელის რამდენიმე declaration, განსაკუთრებით interface-ები."
                },
                {
                    question: "რა არის Template Literal Types?",
                    code: `type EmailLocaleIDs = 'welcome_email' | 'email_heading';
type FooterLocaleIDs = 'footer_title' | 'footer_sendoff';

type AllLocaleIDs = \`\${EmailLocaleIDs | FooterLocaleIDs}_id\`;
// 'welcome_email_id' | 'email_heading_id' | 'footer_title_id' | 'footer_sendoff_id'`,
                    options: [
                        "Template literal types საშუალებას იძლევა string literal types-ის კომბინირება",
                        "Template literal types იყენება მხოლოდ debugging-ისთვის",
                        "Template literal types არ მუშაობს union types-თან",
                        "Template literal types არ არის TypeScript 4.1+-ში"
                    ],
                    correct: 0,
                    explanation: "Template literal types საშუალებას იძლევა template literal syntax-ის გამოყენება type system-ში string literal types-ის კომბინირებისთვის."
                },
                {
                    question: "რა არის Key Remapping in Mapped Types?",
                    code: `type Getters<Type> = {
    [Property in keyof Type as \`get\${Capitalize<string & Property>}\`]: () => Type[Property]
};

interface Person {
    name: string;
    age: number;
}

type PersonGetters = Getters<Person>;
// { getName: () => string; getAge: () => number; }`,
                    options: [
                        "Key remapping საშუალებას იძლევა property names-ის transformation-ს mapped types-ში",
                        "Key remapping იყენება მხოლოდ arrays-თან",
                        "Key remapping არ შეიძლება template literals-თან",
                        "Key remapping არ არის TypeScript-ის ნაწილი"
                    ],
                    correct: 0,
                    explanation: "Key remapping mapped types-ში საშუალებას იძლევა property keys-ის transformation-ისა template literal types-ის გამოყენებით."
                },
                {
                    question: "რა არის Recursive Types?",
                    code: `type JSONValue =
    | string
    | number
    | boolean
    | null
    | JSONValue[]
    | { [key: string]: JSONValue };

const data: JSONValue = {
    name: 'test',
    values: [1, 2, { nested: true }]
};`,
                    options: [
                        "Recursive types საშუალებას იძლევა type-ის თავისი თავის reference-ისა",
                        "Recursive types იყენება მხოლოდ arrays-თან",
                        "Recursive types აცდენს infinite loop-ს",
                        "Recursive types არ არის მხარდაჭერილი"
                    ],
                    correct: 0,
                    explanation: "Recursive types საშუალებას იძლევა type-ის განსაზღვრისა, რომელიც reference-ს ხდის თავის თავზე, როგორიცაა nested data structures-ებისთვის."
                },
                {
                    question: "რა არის Const Assertions?",
                    code: `const colors = ['red', 'green', 'blue'] as const;
// type: readonly ['red', 'green', 'blue']

const config = {
    apiUrl: 'https://api.example.com',
    timeout: 5000
} as const;
// type: { readonly apiUrl: 'https://api.example.com'; readonly timeout: 5000; }`,
                    options: [
                        "as const ქმნის immutable literal types-ს",
                        "as const იყენება მხოლოდ debugging-ისთვის",
                        "as const აცდენს compilation error-ს",
                        "as const არ ცვლის type-ს"
                    ],
                    correct: 0,
                    explanation: "Const assertions (as const) TypeScript-ს ეუბნება რომ treat-ი გააკეთოს literal-ები როგორც immutable და narrow type-ები specific literal values-მდე."
                },
                {
                    question: "რა არის Branded Types?",
                    code: `type UserId = string & { readonly brand: unique symbol };
type ProductId = string & { readonly brand: unique symbol };

function getUserId(id: string): UserId {
    return id as UserId;
}

function getProduct(id: ProductId) { /* ... */ }

const userId = getUserId('user123');
getProduct(userId); // Error: UserId არ არის assignable ProductId-ზე`,
                    options: [
                        "Branded types ქმნის nominal typing-ს primitive types-ისთვის",
                        "Branded types იყენება მხოლოდ performance-ისთვის",
                        "Branded types არ არის TypeScript pattern",
                        "Branded types ვერ ხელს უშლის type mixing-ს"
                    ],
                    correct: 0,
                    explanation: "Branded types (ან Nominal types) ქმნის unique types-ს primitive types-ის base-ზე, რაც ხელს უშლის მსგავსი types-ის შემთხვევით შერევას."
                }
            ]
        };

        // Quiz State
        let currentQuiz = null;
        let currentQuestionIndex = 0;
        let score = 0;
        let selectedAnswers = [];
        let startTime = null;
        let answeredQuestions = new Set();

        // DOM Elements
        const mainMenu = document.getElementById('mainMenu');
        const quizInterface = document.getElementById('quizInterface');
        const resultsScreen = document.getElementById('resultsScreen');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            updateStats();
        });

        function startQuiz(quizType) {
            currentQuiz = quizType;
            currentQuestionIndex = 0;
            score = 0;
            selectedAnswers = [];
            answeredQuestions.clear();
            startTime = Date.now();

            mainMenu.classList.add('hidden');
            quizInterface.classList.remove('hidden');
            resultsScreen.classList.add('hidden');

            showQuestion();
        }

        function showQuestion() {
            const questions = quizData[currentQuiz];
            const question = questions[currentQuestionIndex];

            document.getElementById('currentQuestion').textContent = currentQuestionIndex + 1;
            document.getElementById('totalQuestions').textContent = questions.length;
            document.getElementById('currentScore').textContent = score;
            document.getElementById('questionTitle').textContent = question.question;

            // Show code if available
            const codeBlock = document.getElementById('codeBlock');
            if (question.code) {
                codeBlock.style.display = 'block';
                codeBlock.querySelector('code').textContent = question.code;
            } else {
                codeBlock.style.display = 'none';
            }

            // Update progress bar
            const progress = ((currentQuestionIndex) / questions.length) * 100;
            document.getElementById('progressBar').style.width = progress + '%';

            // Create options
            const optionsContainer = document.getElementById('optionsContainer');
            optionsContainer.innerHTML = '';

            question.options.forEach((option, index) => {
                const button = document.createElement('button');
                button.className = 'option-btn w-full text-left p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors';
                button.innerHTML = `
                    <div class="flex items-center">
                        <span class="font-semibold text-blue-600 mr-3">${String.fromCharCode(65 + index)}.</span>
                        <span>${option}</span>
                    </div>
                `;
                button.onclick = () => selectAnswer(index);
                optionsContainer.appendChild(button);
            });

            // Hide explanation
            document.getElementById('explanationCard').classList.add('hidden');
        }

        function selectAnswer(selectedIndex) {
            if (answeredQuestions.has(currentQuestionIndex)) return;

            const questions = quizData[currentQuiz];
            const question = questions[currentQuestionIndex];
            const options = document.querySelectorAll('.option-btn');

            answeredQuestions.add(currentQuestionIndex);
            selectedAnswers[currentQuestionIndex] = selectedIndex;

            // Mark correct/incorrect
            options.forEach((option, index) => {
                if (index === question.correct) {
                    option.classList.add('correct');
                } else if (index === selectedIndex) {
                    option.classList.add('incorrect');
                }
                option.style.pointerEvents = 'none';
            });

            // Update score
            if (selectedIndex === question.correct) {
                score++;
                document.getElementById('currentScore').textContent = score;
            }

            // Show explanation
            document.getElementById('explanationText').textContent = question.explanation;
            document.getElementById('explanationCard').classList.remove('hidden');
        }

        function nextQuestion() {
            currentQuestionIndex++;
            const questions = quizData[currentQuiz];

            if (currentQuestionIndex >= questions.length) {
                showResults();
            } else {
                showQuestion();
            }
        }

        function showResults() {
            quizInterface.classList.add('hidden');
            resultsScreen.classList.remove('hidden');

            const questions = quizData[currentQuiz];
            const percentage = Math.round((score / questions.length) * 100);
            const timeTaken = Math.round((Date.now() - startTime) / 1000 / 60 * 10) / 10;

            document.getElementById('finalScore').textContent = percentage + '%';
            document.getElementById('correctAnswers').textContent = score;
            document.getElementById('wrongAnswers').textContent = questions.length - score;
            document.getElementById('timeTaken').textContent = timeTaken + ' წთ';

            // Set result message and icon
            let resultTitle = '';
            let resultIcon = '';

            if (percentage >= 90) {
                resultTitle = 'შესანიშნავი!';
                resultIcon = '🏆';
            } else if (percentage >= 70) {
                resultTitle = 'ძალიან კარგი!';
                resultIcon = '🎉';
            } else if (percentage >= 50) {
                resultTitle = 'კარგია!';
                resultIcon = '👍';
            } else {
                resultTitle = 'კიდევ იმუშავეთ!';
                resultIcon = '📚';
            }

            document.getElementById('resultTitle').textContent = resultTitle;
            document.getElementById('resultIcon').textContent = resultIcon;

            // Save stats
            saveQuizResult(currentQuiz, percentage, timeTaken);
            updateStats();
        }

        function restartQuiz() {
            startQuiz(currentQuiz);
        }

        function backToMenu() {
            mainMenu.classList.remove('hidden');
            quizInterface.classList.add('hidden');
            resultsScreen.classList.add('hidden');
        }

        // Statistics Functions
        function saveQuizResult(quizType, percentage, timeTaken) {
            let stats = JSON.parse(localStorage.getItem('quizStats') || '{}');
           
            if (!stats[quizType]) {
                stats[quizType] = { scores: [], totalTime: 0 };
            }
           
            stats[quizType].scores.push(percentage);
            stats[quizType].totalTime += timeTaken;
           
            localStorage.setItem('quizStats', JSON.stringify(stats));
        }

        function loadStats() {
            return JSON.parse(localStorage.getItem('quizStats') || '{}');
        }

        function updateStats() {
            const stats = loadStats();
            const categories = Object.keys(stats);
           
            let totalQuizzes = 0;
            let totalScore = 0;
            let totalTime = 0;
            let bestCategory = '';
            let bestScore = 0;

            categories.forEach(category => {
                const categoryStats = stats[category];
                totalQuizzes += categoryStats.scores.length;
                totalScore += categoryStats.scores.reduce((a, b) => a + b, 0);
                totalTime += categoryStats.totalTime;

                const avgScore = categoryStats.scores.reduce((a, b) => a + b, 0) / categoryStats.scores.length;
                if (avgScore > bestScore) {
                    bestScore = avgScore;
                    bestCategory = getCategoryName(category);
                }
            });

            document.getElementById('totalQuizzes').textContent = totalQuizzes;
            document.getElementById('averageScore').textContent = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) + '%' : '0%';
            document.getElementById('bestCategory').textContent = bestCategory || '-';
            document.getElementById('timeSpent').textContent = Math.round(totalTime) + ' წთ';
        }

        function getCategoryName(category) {
            const names = {
                'jsBeginner': 'JS დამწყები',
                'jsAdvanced': 'JS მოწინავე',
                'tsBeginner': 'TS დამწყები',
                'tsAdvanced': 'TS მოწინავე'
            };
            return names[category] || category;
        }
    </script>
</body>
</html>

